from ast import Dict
import json
import os
import re
import sys
import traceback
import asyncio
from pathlib import Path
from typing import Union

sys.path.append(str(Path(__file__).parent.parent.parent.parent))

from app.core.config import AUTH_PROPERTIES, SERVICES_PROPERTIES, SETTINGS
from app.services.new_query_agent.utils.dynamic_query_validator import QueryValidator


from google import genai
from google.genai import types as genai_types

BASE_PATH = os.path.dirname(os.path.abspath(__file__))


def _extract_json_from_llm_output(llm_output: str) -> dict:
    """Extract and validate JSON from LLM output."""
    # print(f"Raw LLM output: {llm_output}")

    try:
        # Convert AIMessage to string if needed
        if hasattr(llm_output, "content"):
            llm_output = llm_output.content

        # Try to find JSON content between triple backticks if present
        if "```json" in llm_output:
            start = llm_output.find("```json") + 7
            end = llm_output.find("```", start)
            if end != -1:
                llm_output = llm_output[start:end]
        elif "```" in llm_output:
            start = llm_output.find("```") + 3
            end = llm_output.find("```", start)
            if end != -1:
                llm_output = llm_output[start:end]

        # Clean and format the JSON string
        llm_output = llm_output.strip()

        # Only handle unquoted keys
        llm_output = re.sub(
            r"([{,])\s*([a-zA-Z_][a-zA-Z0-9_]*)\s*:", r'\1"\2":', llm_output
        )
        return json.loads(llm_output)
    except json.JSONDecodeError as e:
        print(f"JSON decode error: {e}")
        print(f"Failed JSON string: {llm_output}")
        return {}
    except Exception as e:
        print(f"Unexpected error: {e}")
        return {}


class DynamicQueryGenerator:
    def __init__(self, previous_messages:Union[Dict, list[Dict]] = []) -> None:
        self.genai_client = genai.Client(
            vertexai=True,
            project=AUTH_PROPERTIES.GOOGLE_CLOUD_PROJECT,
            location=AUTH_PROPERTIES.GOOGLE_CLOUD_LOCATION,
        )

        prompt_file_path = os.path.join(BASE_PATH, "./prompts/dynamic_query_prompt.txt")

        with open(prompt_file_path, "r") as fp:
            prompt = fp.read()

        self.generation_config = genai_types.GenerateContentConfig(
            temperature=0,
            system_instruction=prompt,
        )
        self.messages = []
        if previous_messages:
            self.messages.append(
                genai_types.Content(
                    role="user",
                    parts=[
                        {
                            "text": f"Previous messages between user and the model is given below \n {previous_messages}"
                        }
                    ],
                )
            )

    async def generate(self, user_query, max_tries=2):

        messages = self.messages
        messages.append(
            genai_types.Content(
                role="user",
                parts=[{"text": user_query}],
            )
        )
        try:
            for _ in range(max_tries):
                response = await self.genai_client.aio.models.generate_content(
                    model="gemini-2.0-flash",
                    contents=messages,
                    config=self.generation_config,
                )

                query_payload = _extract_json_from_llm_output(response.text)
                validator = QueryValidator()
                is_valid, errors = validator.validate(query_payload)
                if is_valid:
                    return query_payload
                else:
                    print(f"Validation errors found.\n {errors} \n Retrying...")

                    messages.extend(
                        [
                            genai_types.Content(
                                role="model",
                                parts=[{"text": response.text}],
                            ),
                            genai_types.Content(
                                role="user",
                                parts=[
                                    {
                                        "text": f"The query is not valid. Please correct the query. \n Errors \n {errors}"
                                    }
                                ],
                            ),
                        ]
                    )
        except Exception as e:
            print("Error while generating dynamic query:", e)
            traceback.print_exc()

        return None


if __name__ == "__main__":
    query = "What is my exposure to Nvidia"
    previous_messages = []
    response = asyncio.run(DynamicQueryGenerator(previous_messages).generate(query))
    print("Generated query:", json.dumps(response, indent=2))
