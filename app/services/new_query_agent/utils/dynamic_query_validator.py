import json
from typing import Dict, List, Any, Optional, Set, Union
from dataclasses import dataclass
from enum import StrEnum



class OperationType(StrEnum):
    """Enum for valid operation types"""
    INSERT = "insert"
    SELECT = "select"
    SELECT_SINGLE = "selectsingle"
    UPDATE = "update"
    DELETE = "delete"
    AGGREGATE = "aggregate"
    COUNT = "count"
    DISTINCT = "distinct"
    FILTER = "filter"
    ORDER_BY = "orderBy"
    GROUP_BY = "groupBy"
    LIMIT = "limit"
    OFFSET = "offset"
    JOIN = "join"


@dataclass
class ValidationError:
    """Class to hold validation error details"""
    path: str
    error_type: str
    message: str
    
    def __str__(self):
        return f"[{self.error_type}] at '{self.path}': {self.message}"


class QueryValidator:
    """Main validator class for JSON query structures"""
    
    def __init__(self):
        """
        Initialize validator
        """
        self.errors: List[ValidationError] = []
        
        # Define valid operations
        self.valid_operations = {op.value for op in OperationType}
        
        # Define operations that can contain nested structures
        self.nested_operations = {OperationType.SELECT, OperationType.INSERT, OperationType.UPDATE, OperationType.JOIN}
        
        # Define operations that expect specific value types
        self.operation_value_types = {
            "filter": str,
            "orderBy": str,
            "groupBy": list,
            "limit": int,
            "offset": int,
            "count": bool,
            "distinct": (dict)
        }
        
    def validate(self, query_json: Union[str, Dict]) -> tuple[bool, List[ValidationError]]:
        """
        Validate the JSON query structure
        
        Args:
            query_json: JSON string or dictionary to validate
            
        Returns:
            Tuple of (is_valid, list_of_errors)
        """
        self.errors = []
        
        # Parse JSON if string
        if isinstance(query_json, str):
            try:
                query_dict = json.loads(query_json)
            except json.JSONDecodeError as e:
                self.errors.append(ValidationError(
                    path="root",
                    error_type="JSON_PARSE_ERROR",
                    message=f"Invalid JSON: {str(e)}"
                ))
                return False, self.errors
        else:
            query_dict = query_json
            
        # Validate structure
        self._validate_structure(query_dict, "root")
        
        return len(self.errors) == 0, self.errors
    
    def _validate_structure(self, obj: Any, path: str):
        """Recursively validate the query structure"""
        
        if not isinstance(obj, dict):
            self.errors.append(ValidationError(
                path=path,
                error_type="TYPE_ERROR",
                message=f"Expected dictionary but got {type(obj).__name__}"
            ))
            return
            
        # Check if this is a table-level object
        if path == "root":
            # Root should contain table names
            if not obj:
                self.errors.append(ValidationError(
                    path=path,
                    error_type="EMPTY_QUERY",
                    message="Query cannot be empty"
                ))
                return
                
            for table_name, table_ops in obj.items():
                self._validate_table_operations(table_ops, f"{path}.{table_name}")
        else:
            # This might be a nested structure
            self._validate_nested_structure(obj, path)
    
    def _validate_table_operations(self, table_ops: Any, path: str):
        """Validate operations for a specific table"""

        if not isinstance(table_ops, dict):
            self.errors.append(ValidationError(
                path=path,
                error_type="TYPE_ERROR",
                message=f"Table operations must be a dictionary, got {type(table_ops).__name__}"
            ))
            return

        # Check if distinct operation is present
        has_distinct = OperationType.DISTINCT in table_ops
        if has_distinct and len(table_ops) > 1:
            other_operations = [op for op in table_ops.keys() if op != OperationType.DISTINCT]
            self.errors.append(ValidationError(
                path=path,
                error_type="OPERATION_CONFLICT",
                message=f"When 'distinct' operation is present, no other operations are allowed. Found: {', '.join(other_operations)}"
            ))
            return

        # Check for invalid operations
        for op_name, op_value in table_ops.items():
            if op_name not in self.valid_operations:
                self.errors.append(ValidationError(
                    path=f"{path}.{op_name}",
                    error_type="INVALID_OPERATION",
                    message=f"'{op_name}' is not a valid operation. Valid operations are: {', '.join(sorted(self.valid_operations))}"
                ))
                continue

            # Validate operation values
            self._validate_operation_value(op_name, op_value, f"{path}.{op_name}")
    
    def _validate_operation_value(self, op_name: str, op_value: Any, path: str):
        """Validate the value for a specific operation"""

        # Check nested operations
        if op_name in self.nested_operations:
            if op_name == OperationType.SELECT:
                self._validate_select_structure(op_value, path)
            elif op_name == OperationType.INSERT:
                self._validate_insert_structure(op_value, path)
            elif op_name == OperationType.UPDATE:
                self._validate_update_structure(op_value, path)
            elif op_name == OperationType.JOIN:
                self._validate_join_structure(op_value, path)

        # Check value type operations
        elif op_name in self.operation_value_types:
            expected_type = self.operation_value_types[op_name]
            if not isinstance(op_value, expected_type):
                if isinstance(expected_type, tuple):
                    type_names = " or ".join(t.__name__ for t in expected_type)
                else:
                    type_names = expected_type.__name__

                self.errors.append(ValidationError(
                    path=path,
                    error_type="TYPE_ERROR",
                    message=f"Operation '{op_name}' expects {type_names}, got {type(op_value).__name__}"
                ))
            elif op_name == OperationType.GROUP_BY:
                self._validate_group_by_fields(op_value, path)
    
    def _validate_select_structure(self, select_obj: Any, path: str):
        """Validate SELECT operation structure"""
        
        if not isinstance(select_obj, dict):
            self.errors.append(ValidationError(
                path=path,
                error_type="TYPE_ERROR",
                message=f"SELECT operation must be a dictionary, got {type(select_obj).__name__}"
            ))
            return
            
        for field_name, field_value in select_obj.items():
            field_path = f"{path}.{field_name}"
            
            # Field value can be:
            # 1. Boolean (true/false for simple selection)
            # 2. Dictionary (for nested selection with possible filter)
            
            if isinstance(field_value, bool):
                # Simple field selection
                pass
            elif isinstance(field_value, dict):
                # Nested selection
                if OperationType.SELECT in field_value:
                    self._validate_select_structure(field_value["select"], f"{field_path}.select")
                    
                if "filter" in field_value:
                    if not isinstance(field_value["filter"], str):
                        self.errors.append(ValidationError(
                            path=f"{field_path}.filter",
                            error_type="TYPE_ERROR",
                            message=f"Filter must be a string, got {type(field_value['filter']).__name__}"
                        ))
                        
                # Check for invalid keys in nested selection
                valid_nested_keys = {"select", "filter"}
                invalid_keys = set(field_value.keys()) - valid_nested_keys
                if invalid_keys:
                    self.errors.append(ValidationError(
                        path=field_path,
                        error_type="INVALID_KEYS",
                        message=f"Invalid keys in nested selection: {', '.join(invalid_keys)}. Valid keys are: {', '.join(valid_nested_keys)}"
                    ))
            else:
                self.errors.append(ValidationError(
                    path=field_path,
                    error_type="TYPE_ERROR",
                    message=f"Field value must be boolean or dictionary, got {type(field_value).__name__}"
                ))
    
    def _validate_insert_structure(self, insert_obj: Any, path: str):
        """Validate INSERT operation structure"""
        
        if not isinstance(insert_obj, (dict, list)):
            self.errors.append(ValidationError(
                path=path,
                error_type="TYPE_ERROR",
                message=f"INSERT operation must be a dictionary or list, got {type(insert_obj).__name__}"
            ))
            return
            
        # Additional INSERT validation logic can be added here
    
    def _validate_update_structure(self, update_obj: Any, path: str):
        """Validate UPDATE operation structure"""
        
        if not isinstance(update_obj, dict):
            self.errors.append(ValidationError(
                path=path,
                error_type="TYPE_ERROR",
                message=f"UPDATE operation must be a dictionary, got {type(update_obj).__name__}"
            ))
            return
            
        # Additional UPDATE validation logic can be added here
    
    def _validate_join_structure(self, join_obj: Any, path: str):
        """Validate JOIN operation structure"""
        
        if not isinstance(join_obj, (dict, list)):
            self.errors.append(ValidationError(
                path=path,
                error_type="TYPE_ERROR",
                message=f"JOIN operation must be a dictionary or list, got {type(join_obj).__name__}"
            ))
            return
            
        # Additional JOIN validation logic can be added here
    
    def _validate_group_by_fields(self, group_by_value: Any, path: str):
        """Validate groupBy fields to ensure only first-level fields are used"""

        def check_field_for_dots(field: str, field_path: str):
            """Check if a field contains dots (nested field paths)"""
            if '.' in field:
                self.errors.append(ValidationError(
                    path=field_path,
                    error_type="NESTED_FIELD_ERROR",
                    message=f"groupBy only supports first-level fields. Nested field paths like '{field}' are not allowed. Use only field names without dots."
                ))

        # groupBy must be a list of strings
        if isinstance(group_by_value, list):
            for i, field in enumerate(group_by_value):
                if isinstance(field, str):
                    check_field_for_dots(field, f"{path}[{i}]")
                else:
                    self.errors.append(ValidationError(
                        path=f"{path}[{i}]",
                        error_type="TYPE_ERROR",
                        message=f"groupBy field must be a string, got {type(field).__name__}"
                    ))

    def _validate_nested_structure(self, obj: Dict, path: str):
        """Validate nested structures within operations"""

        # This method can be extended for specific nested structure validation
        pass


def validate_query(query_json: Union[str, Dict], 
                  print_errors: bool = True) -> bool:
    """
    Convenience function to validate a query
    
    Args:
        query_json: JSON string or dictionary to validate
        print_errors: Whether to print errors to console
        
    Returns:
        Boolean indicating if query is valid
    """
    validator = QueryValidator()
    is_valid, errors = validator.validate(query_json)
    
    if print_errors and errors:
        print("Validation Errors Found:")
        print("-" * 50)
        for error in errors:
            print(f"  {error}")
        print("-" * 50)
        print(f"Total errors: {len(errors)}")
    elif print_errors and is_valid:
        print("✓ Query structure is valid!")
        
    return is_valid


# Example usage and testing
if __name__ == "__main__":
    # Your example query
    example_query = {
        "Account": {
            "select": {
                "id": True,
                "accountNumber": True,
                "primaryOwner": {
                    "select": {
                        "firstName": True,
                        "lastName": True
                    },
                    "filter": "firstName == 'Alice' "
                }
            },
            "filter": "(accountNumber == '1234' || accountNumber == '5678') && balance > 1000",
            "orderBy": "desc(accountNumber)"
        }
    }
    
    # Valid query test
    print("Testing valid query:")
    validate_query(example_query)
    
    print("\n" + "="*60 + "\n")

    # Valid groupBy query (first-level fields only)
    valid_group_by_query = {
        "Account": {
            "select": {"id": True, "accountType": True, "balance": True},
            "groupBy": ["accountType", "status"]
        }
    }

    print("Testing valid groupBy query:")
    validate_query(valid_group_by_query)

    print("\n" + "="*60 + "\n")

    # Invalid query examples
    invalid_queries = [
        # Invalid operation
        {
            "Account": {
                "select": {"id": True},
                "invalidOp": "test"
            }
        },
        # Wrong type for filter
        {
            "Account": {
                "select": {"id": True},
                "filter": {"invalid": "object"}
            }
        },
        # Wrong type for limit
        {
            "Account": {
                "select": {"id": True},
                "limit": "should_be_number"
            }
        },
        # Invalid nested structure
        {
            "Account": {
                "select": {
                    "id": True,
                    "nested": {
                        "invalidKey": "value"
                    }
                }
            }
        },
        # Invalid groupBy with string instead of list (should be invalid)
        {
            "Account": {
                "select": {"id": True},
                "groupBy": "accountType"
            }
        },
        # Invalid groupBy with nested field paths (should be invalid)
        {
            "Account": {
                "select": {"id": True},
                "groupBy": ["primaryOwner.firstName"]
            }
        },
        # Invalid groupBy with mixed valid and nested fields (should be invalid)
        {
            "Account": {
                "select": {"id": True},
                "groupBy": ["accountType", "primaryOwner.lastName", "status"]
            }
        }
    ]
    
    for i, invalid_query in enumerate(invalid_queries, 1):
        print(f"Testing invalid query {i}:")
        validate_query(invalid_query)
        print("\n" + "="*60 + "\n")

    # Additional groupBy edge case tests
    print("Testing additional groupBy edge cases:")

    # Test single valid field (must be in a list)
    single_field_query = {
        "Account": {
            "select": {"id": True},
            "groupBy": ["accountType"]
        }
    }
    print("Single valid field in list:")
    validate_query(single_field_query)

    print("\n" + "-"*30 + "\n")

    # Test deeply nested field path
    deep_nested_query = {
        "Account": {
            "select": {"id": True},
            "groupBy": ["owner.address.city.zipCode"]
        }
    }
    print("Deeply nested field path:")
    validate_query(deep_nested_query)

    print("\n" + "-"*30 + "\n")

    # Test non-string values in groupBy list
    non_string_query = {
        "Account": {
            "select": {"id": True},
            "groupBy": ["accountType", 123, "status"]
        }
    }
    print("Non-string values in groupBy list:")
    validate_query(non_string_query)